<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Quick Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Quick Test</h1>
    <button onclick="quickTest()">Quick Test</button>
    <div id="results"></div>

    <script>
        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            console.log(msg);
        }

        async function quickTest() {
            document.getElementById('results').innerHTML = '';
            log('🔍 اختبار سريع...', 'info');
            
            try {
                // تحميل BugBountyCore
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const core = new BugBountyCore();
                
                // تحميل القالب مباشرة
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                
                log(`✅ تم تحضير النظام - القالب: ${templateHTML.length} حرف`, 'success');
                
                // اختبار سريع للدالة
                const testData = {
                    total_vulnerabilities: 1,
                    vulnerabilities: [
                        { name: 'Test XSS', severity: 'High', description: 'Test vulnerability' }
                    ]
                };
                
                log('🧪 اختبار generateFinalComprehensiveReport...', 'info');
                
                // تسجيل console.log
                const originalLog = console.log;
                const logs = [];
                console.log = (...args) => {
                    logs.push(args.join(' '));
                    originalLog(...args);
                };
                
                try {
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://test.com');
                    console.log = originalLog;
                    
                    if (report && report.length > 1000) {
                        log(`✅ نجح! حجم التقرير: ${report.length} حرف`, 'success');
                        
                        const hasVars = report.includes('{{');
                        if (!hasVars) {
                            log('🎉 تم استبدال جميع المتغيرات!', 'success');
                        } else {
                            const vars = report.match(/{{[^}]+}}/g);
                            log(`⚠️ متغيرات متبقية: ${vars ? vars.slice(0, 3).join(', ') : 'غير معروف'}`, 'info');
                        }
                        
                        // إنشاء رابط تحميل
                        const blob = new Blob([report], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'quick-test-report.html';
                        link.textContent = 'تحميل التقرير النهائي';
                        link.style.display = 'block';
                        link.style.margin = '10px 0';
                        link.style.padding = '10px';
                        link.style.background = '#28a745';
                        link.style.color = 'white';
                        link.style.textDecoration = 'none';
                        link.style.borderRadius = '5px';
                        
                        document.getElementById('results').appendChild(link);
                        
                    } else {
                        log('❌ فشل في إنشاء التقرير', 'error');
                        
                        // عرض آخر سجلات
                        log('📋 آخر سجلات:', 'info');
                        logs.slice(-5).forEach(logEntry => {
                            log(`   ${logEntry}`, 'info');
                        });
                    }
                    
                } catch (reportError) {
                    console.log = originalLog;
                    log(`❌ خطأ: ${reportError.message}`, 'error');
                    
                    // عرض السجلات المهمة
                    log('📋 سجلات مهمة:', 'info');
                    const importantLogs = logs.filter(logEntry => 
                        logEntry.includes('فحص نهائي') || 
                        logEntry.includes('templateHTML') ||
                        logEntry.includes('القالب') ||
                        logEntry.includes('خطأ') ||
                        logEntry.includes('فشل')
                    );
                    
                    importantLogs.slice(-10).forEach(logEntry => {
                        log(`   ${logEntry}`, 'info');
                    });
                }
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
