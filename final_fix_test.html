<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Final Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .copy-btn { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 10px 0;
        }
        .diagnostic-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Final Fix Test</h1>
    <button onclick="runFinalTest()">Run Final Test</button>
    <button class="copy-btn" onclick="copyResults()">Copy Results</button>
    
    <div id="results"></div>
    <div id="diagnosticOutput" class="diagnostic-output" style="display: none;"></div>

    <script>
        let testResults = '';

        function log(msg, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${msg}`;
            document.getElementById('results').appendChild(div);
            
            testResults += `[${new Date().toLocaleTimeString()}] [${type.toUpperCase()}] ${msg}\n`;
        }

        function copyResults() {
            const outputDiv = document.getElementById('diagnosticOutput');
            outputDiv.style.display = 'block';
            outputDiv.textContent = testResults;
            
            navigator.clipboard.writeText(testResults).then(() => {
                alert('تم نسخ النتائج للحافظة!');
            }).catch(() => {
                const textArea = document.createElement('textarea');
                textArea.value = testResults;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ النتائج للحافظة!');
            });
        }

        async function runFinalTest() {
            document.getElementById('results').innerHTML = '';
            testResults = '';
            
            log('🔍 اختبار الإصلاح النهائي...', 'info');
            
            try {
                // تحميل BugBountyCore
                if (typeof window.BugBountyCore !== 'undefined') {
                    delete window.BugBountyCore;
                }
                
                const script = document.createElement('script');
                script.src = `./assets/modules/bugbounty/BugBountyCore.js?v=${Date.now()}`;
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (typeof BugBountyCore === 'undefined') {
                    throw new Error('BugBountyCore not loaded');
                }
                
                log('✅ تم تحميل BugBountyCore', 'success');
                
                // إنشاء مثيل
                const core = new BugBountyCore();
                log('✅ تم إنشاء مثيل BugBountyCore', 'success');
                
                // تحميل القالب
                const templateResponse = await fetch('./assets/modules/bugbounty/report_template.html');
                if (!templateResponse.ok) {
                    throw new Error(`فشل تحميل القالب: ${templateResponse.status}`);
                }
                
                const templateHTML = await templateResponse.text();
                core.reportTemplateHTML = templateHTML;
                log(`✅ تم تحميل القالب - الحجم: ${templateHTML.length} حرف`, 'success');
                
                // اختبار الدالة مع بيانات صحيحة
                log('🧪 اختبار مع بيانات صحيحة...', 'info');
                
                const testData = {
                    vulnerabilities: [
                        { name: 'XSS Test', severity: 'High', description: 'Test XSS vulnerability' },
                        { name: 'SQL Test', severity: 'Critical', description: 'Test SQL injection' }
                    ]
                };
                
                try {
                    const report = await core.generateFinalComprehensiveReport(testData, [], 'https://test.com');
                    
                    if (report && report.length > 1000) {
                        log(`✅ نجح مع البيانات الصحيحة! الحجم: ${report.length} حرف`, 'success');
                        
                        const variables = report.match(/{{[^}]+}}/g);
                        if (variables) {
                            log(`⚠️ متغيرات متبقية: ${variables.length} - ${variables.slice(0, 3).join(', ')}`, 'info');
                        } else {
                            log('🎉 تم استبدال جميع المتغيرات!', 'success');
                        }
                        
                        // إنشاء رابط تحميل
                        const blob = new Blob([report], { type: 'text/html' });
                        const url = URL.createObjectURL(blob);
                        
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = 'final-fix-test-report.html';
                        link.textContent = 'تحميل التقرير النهائي';
                        link.style.display = 'block';
                        link.style.margin = '10px 0';
                        link.style.padding = '10px';
                        link.style.background = '#28a745';
                        link.style.color = 'white';
                        link.style.textDecoration = 'none';
                        link.style.borderRadius = '5px';
                        
                        document.getElementById('results').appendChild(link);
                        
                    } else {
                        log('❌ فشل مع البيانات الصحيحة', 'error');
                    }
                    
                } catch (reportError) {
                    log(`❌ خطأ مع البيانات الصحيحة: ${reportError.message}`, 'error');
                }
                
                // اختبار مع بيانات خاطئة (كما في الاختبار السابق)
                log('🧪 اختبار مع بيانات خاطئة...', 'info');
                
                const badTestData = {
                    total_vulnerabilities: 1,
                    vulnerabilities: [
                        { name: 'Test XSS', severity: 'High' }
                    ]
                };
                
                try {
                    const report2 = await core.generateFinalComprehensiveReport(badTestData, [], 'https://test.com');
                    
                    if (report2 && report2.length > 1000) {
                        log(`✅ نجح حتى مع البيانات الخاطئة! الحجم: ${report2.length} حرف`, 'success');
                        log('🎉 تم إصلاح المشكلة نهائياً!', 'success');
                    } else {
                        log('❌ فشل مع البيانات الخاطئة', 'error');
                    }
                    
                } catch (reportError2) {
                    log(`❌ خطأ مع البيانات الخاطئة: ${reportError2.message}`, 'error');
                }
                
                log('✅ انتهى الاختبار - اضغط "Copy Results" لنسخ النتائج', 'success');
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
